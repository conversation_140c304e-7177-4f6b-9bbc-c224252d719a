cmake_minimum_required(VERSION 3.21)
project(tQML)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

set(CMAKE_PREFIX_PATH "C:/Qt/6.9.1/msvc2022_64/lib/cmake")

find_package(Qt6 COMPONENTS
        Core
        Gui
        Widgets
        Quick
        QuickWidgets
        Location
        Positioning
        REQUIRED)

add_executable(tQML main.cpp
        mainwindow.cpp
        mainwindow.h
        mainwindow.ui
        mapview.qml
        PointMarker.qml
        MeasureLine.qml
)
target_link_libraries(tQML
        Qt6::Core
        Qt6::Gui
        Qt6::Widgets
        Qt6::Quick
        Qt6::QuickWidgets
        Qt6::Location
        Qt6::Positioning
)

# 复制QML文件到构建目录
#add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
#        COMMAND ${CMAKE_COMMAND} -E copy_if_different
#        "${CMAKE_SOURCE_DIR}/mapview.qml"
#        "$<TARGET_FILE_DIR:${PROJECT_NAME}>/mapview.qml")
#
#add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
#        COMMAND ${CMAKE_COMMAND} -E copy_if_different
#        "${CMAKE_SOURCE_DIR}/PointMarker.qml"
#        "$<TARGET_FILE_DIR:${PROJECT_NAME}>/PointMarker.qml")
#
#add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
#        COMMAND ${CMAKE_COMMAND} -E copy_if_different
#        "${CMAKE_SOURCE_DIR}/MeasureLine.qml"
#        "$<TARGET_FILE_DIR:${PROJECT_NAME}>/MeasureLine.qml")
