import QtQuick
import QtLocation
import QtPositioning

Rectangle {
    id: mapContainer

    property bool measureMode: false
    property var measurePoints: []

    // 信号定义
    signal moveToBeijing
    signal startMeasure

    // 更新清除按钮的功能
    function clearMap() {
        map.clearMapItems();
        measurePoints = [];
        measureMode = false;
        statusText.text = "地图已清除 - TMS瓦片地图";
    }

    // 响应外部信号
    function moveToBeijingLocation() {
        map.center = QtPositioning.coordinate(39.9042, 116.4074);
        map.zoomLevel = 12;
        statusText.text = "已移动到北京 - TMS瓦片地图";
    }
    function startMeasureMode() {
        measureMode = true;
        measurePoints = [];
        map.clearMapItems();
        statusText.text = "测距模式：请点击第一个点";
    }

    height: 600
    width: 800

    Plugin {
        id: mapPlugin
        name: "osm" // 使用OpenStreetMap

        // 禁用默认提供商仓库
        PluginParameter {
            name: "osm.mapping.providersrepository.disabled"
            value: "true"
        }
        
        // 使用ReadyMap TMS服务器 - 尝试不同的URL格式
        PluginParameter {
            name: "osm.mapping.custom.host"
            value: "http://readymap.org/readymap/tiles/1.0.0/7/"
        }
        
        PluginParameter {
            name: "osm.mapping.custom.mapcopyright"
            value: "© ReadyMap.org"
        }
        PluginParameter {
            name: "osm.mapping.custom.datacopyright"
            value: "© ReadyMap.org"
        }
        PluginParameter {
            name: "osm.useragent"
            value: "Qt Location TMS Map Application"
        }
        
        // 强制使用HTTP/1.1来避免HTTP/2连接问题
        PluginParameter {
            name: "osm.mapping.cache.disk.size"
            value: "50000000"
        }
        PluginParameter {
            name: "osm.mapping.cache.memory.size"
            value: "10000000"
        }
        
        // 尝试设置网络超时
        PluginParameter {
            name: "osm.mapping.cache.texture.size"
            value: "6000000"
        }
    }

    Map {
        id: map

        anchors.fill: parent
        plugin: mapPlugin
        center: QtPositioning.coordinate(39.9042, 116.4074) // 北京坐标
        zoomLevel: 10

        // 设置缩放范围
        minimumZoomLevel: 1
        maximumZoomLevel: 18

        // 使用自定义瓦片地图
        activeMapType: {
            if (supportedMapTypes.length > 0) {
                return supportedMapTypes[supportedMapTypes.length - 1]
            }
            return supportedMapTypes[0]
        }

        property geoCoordinate startCentroid

        // 地图加载完成后的处理
        Component.onCompleted: {
            console.log("支持的地图类型数量:", supportedMapTypes.length);
            for (var i = 0; i < supportedMapTypes.length; i++) {
                console.log("地图类型", i, ":", supportedMapTypes[i].name, "样式:", supportedMapTypes[i].style);
            }
            if (supportedMapTypes.length > 0) {
                console.log("当前使用地图类型:", activeMapType.name);
                console.log("当前地图样式:", activeMapType.style);
                statusText.text = "ReadyMap TMS瓦片地图已加载";
            } else {
                statusText.text = "地图加载失败 - 无可用地图类型";
            }
        }

        // 错误处理
        onErrorChanged: {
            if (error !== Map.NoError) {
                console.log("地图错误:", error);
                statusText.text = "地图加载错误: " + error;
            }
        }

        // 缩放手势处理（双指缩放）
        PinchHandler {
            id: pinch
            enabled: !measureMode
            grabPermissions: PointerHandler.TakeOverForbidden
            target: null

            onActiveChanged: if (active) {
                map.startCentroid = map.toCoordinate(pinch.centroid.position, false);
            }
            onRotationChanged: delta => {
                map.bearing -= delta;
                map.alignCoordinateToPoint(map.startCentroid, pinch.centroid.position);
            }
            onScaleChanged: delta => {
                map.zoomLevel += Math.log2(delta);
                map.alignCoordinateToPoint(map.startCentroid, pinch.centroid.position);
            }
        }

        // 鼠标滚轮缩放处理
        WheelHandler {
            id: wheel
            acceptedDevices: Qt.platform.pluginName === "cocoa" || Qt.platform.pluginName === "wayland" ? PointerDevice.Mouse | PointerDevice.TouchPad : PointerDevice.Mouse
            enabled: !measureMode
            property: "zoomLevel"
            rotationScale: 1 / 120
        }

        // 拖动手势处理
        DragHandler {
            id: drag
            enabled: !measureMode
            target: null
            onTranslationChanged: delta => map.pan(-delta.x, -delta.y)
        }

        // 处理鼠标点击事件（仅在测距模式下）
        MouseArea {
            anchors.fill: parent
            enabled: measureMode

            onClicked: {
                if (measureMode) {
                    var coord = map.toCoordinate(Qt.point(mouse.x, mouse.y));
                    measurePoints.push(coord);

                    if (measurePoints.length === 1) {
                        // 添加第一个点标记
                        var marker1 = Qt.createQmlObject(`
                            import QtQuick
                            import QtLocation
                            MapQuickItem {
                                anchorPoint.x: sourceItem.width / 2
                                anchorPoint.y: sourceItem.height
                                sourceItem: Rectangle {
                                    width: 20; height: 20; radius: 10
                                    color: "red"; border.color: "darkred"; border.width: 2
                                    Text { anchors.centerIn: parent; text: "1"; color: "white"; font.bold: true }
                                }
                            }`, map, "marker1");
                        marker1.coordinate = coord;
                        map.addMapItem(marker1);
                        statusText.text = "请点击第二个点";
                    } else if (measurePoints.length === 2) {
                        // 添加第二个点标记
                        var marker2 = Qt.createQmlObject(`
                            import QtQuick
                            import QtLocation
                            MapQuickItem {
                                anchorPoint.x: sourceItem.width / 2
                                anchorPoint.y: sourceItem.height
                                sourceItem: Rectangle {
                                    width: 20; height: 20; radius: 10
                                    color: "blue"; border.color: "darkblue"; border.width: 2
                                    Text { anchors.centerIn: parent; text: "2"; color: "white"; font.bold: true }
                                }
                            }`, map, "marker2");
                        marker2.coordinate = coord;
                        map.addMapItem(marker2);

                        // 计算距离
                        var distance = measurePoints[0].distanceTo(measurePoints[1]);
                        var distanceKm = (distance / 1000).toFixed(2);

                        // 添加连线
                        var polyline = Qt.createQmlObject(`
                            import QtQuick
                            import QtLocation
                            MapPolyline {
                                line.width: 3; line.color: "blue"; opacity: 0.8
                            }`, map, "polyline");
                        polyline.path = measurePoints;
                        map.addMapItem(polyline);

                        statusText.text = "距离: " + distanceKm + " 公里";
                        measureMode = false;
                        measurePoints = [];
                    }
                }
            }
        }
    }

    // 状态文本
    Rectangle {
        id: statusBar
        anchors.bottom: parent.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        color: "lightgray"
        height: 60
        opacity: 0.8

        Column {
            anchors.centerIn: parent
            spacing: 2

            Text {
                id: statusText
                anchors.horizontalCenter: parent.horizontalCenter
                font.pixelSize: 14
                text: "正在加载ReadyMap TMS瓦片地图..."
            }
            Text {
                id: mapInfoText
                anchors.horizontalCenter: parent.horizontalCenter
                color: "darkgray"
                font.pixelSize: 10
                text: "缩放级别: " + map.zoomLevel.toFixed(1) + " | 中心: " + map.center.latitude.toFixed(4) + ", " + map.center.longitude.toFixed(4)
            }
        }
    }

    // 清除按钮
    Rectangle {
        id: clearButton
        anchors.margins: 10
        anchors.right: parent.right
        anchors.top: parent.top
        border.color: "blue"
        color: "lightblue"
        height: 30
        radius: 5
        width: 80

        Text {
            anchors.centerIn: parent
            font.pixelSize: 12
            text: "清除"
        }
        MouseArea {
            anchors.fill: parent
            onClicked: {
                clearMap();
            }
        }
    }
}
