import QtQuick
import QtQuick.Controls
import QtLocation
import QtPositioning

ApplicationWindow {
    id: window
    width: 1200
    height: 800
    visible: true
    title: "TMS瓦片地图测试 - 多服务器对比"

    Rectangle {
        anchors.fill: parent
        color: "lightgray"

        // 顶部控制面板
        Rectangle {
            id: controlPanel
            anchors.top: parent.top
            anchors.left: parent.left
            anchors.right: parent.right
            height: 60
            color: "white"
            border.color: "gray"

            Row {
                anchors.centerIn: parent
                spacing: 20

                Text {
                    anchors.verticalCenter: parent.verticalCenter
                    text: "选择地图服务器:"
                    font.bold: true
                    font.pixelSize: 14
                }

                Button {
                    text: "OpenStreetMap"
                    onClicked: {
                        mapLoader.source = "mapview.qml"
                        statusLabel.text = "当前使用: OpenStreetMap 瓦片服务器"
                    }
                }

                Button {
                    text: "ReadyMap TMS"
                    onClicked: {
                        mapLoader.source = "mapview_readymap.qml"
                        statusLabel.text = "当前使用: ReadyMap TMS 瓦片服务器"
                    }
                }

                Button {
                    text: "重新加载"
                    onClicked: {
                        mapLoader.source = ""
                        mapLoader.source = mapLoader.currentSource
                    }
                }
            }
        }

        // 状态标签
        Text {
            id: statusLabel
            anchors.top: controlPanel.bottom
            anchors.left: parent.left
            anchors.margins: 10
            text: "当前使用: OpenStreetMap 瓦片服务器"
            font.pixelSize: 12
            color: "darkblue"
        }

        // 地图加载器
        Loader {
            id: mapLoader
            anchors.top: statusLabel.bottom
            anchors.bottom: infoPanel.top
            anchors.left: parent.left
            anchors.right: parent.right
            anchors.margins: 10
            source: "mapview.qml"
            
            property string currentSource: source
            
            onLoaded: {
                console.log("地图组件已加载:", source)
                currentSource = source
            }
            
            onSourceChanged: {
                console.log("地图源已更改为:", source)
            }
        }

        // 底部信息面板
        Rectangle {
            id: infoPanel
            anchors.bottom: parent.bottom
            anchors.left: parent.left
            anchors.right: parent.right
            height: 120
            color: "white"
            border.color: "gray"

            Column {
                anchors.fill: parent
                anchors.margins: 10
                spacing: 5

                Text {
                    text: "测试说明:"
                    font.bold: true
                    font.pixelSize: 14
                }

                Text {
                    text: "1. OpenStreetMap: 使用标准OSM瓦片服务器 (https://tile.openstreetmap.org)"
                    font.pixelSize: 11
                    wrapMode: Text.WordWrap
                    width: parent.width
                }

                Text {
                    text: "2. ReadyMap TMS: 使用ReadyMap的TMS瓦片服务器 (http://readymap.org/readymap/tiles/1.0.0/7/)"
                    font.pixelSize: 11
                    wrapMode: Text.WordWrap
                    width: parent.width
                }

                Text {
                    text: "如果遇到HTTP/2连接错误，请尝试切换到OpenStreetMap服务器"
                    font.pixelSize: 11
                    color: "red"
                    wrapMode: Text.WordWrap
                    width: parent.width
                }

                Row {
                    spacing: 20
                    
                    Button {
                        text: "移动到北京"
                        onClicked: {
                            if (mapLoader.item) {
                                mapLoader.item.moveToBeijingLocation()
                            }
                        }
                    }

                    Button {
                        text: "开始测距"
                        onClicked: {
                            if (mapLoader.item) {
                                mapLoader.item.startMeasureMode()
                            }
                        }
                    }

                    Button {
                        text: "清除地图"
                        onClicked: {
                            if (mapLoader.item) {
                                mapLoader.item.clearMap()
                            }
                        }
                    }
                }
            }
        }

        // 加载指示器
        Rectangle {
            anchors.centerIn: mapLoader
            width: 200
            height: 60
            color: "white"
            border.color: "gray"
            radius: 10
            visible: mapLoader.status === Loader.Loading

            Column {
                anchors.centerIn: parent
                spacing: 10

                Text {
                    anchors.horizontalCenter: parent.horizontalCenter
                    text: "正在加载地图..."
                    font.pixelSize: 14
                }

                Rectangle {
                    anchors.horizontalCenter: parent.horizontalCenter
                    width: 100
                    height: 4
                    color: "lightgray"
                    radius: 2

                    Rectangle {
                        width: parent.width * 0.7
                        height: parent.height
                        color: "blue"
                        radius: 2

                        PropertyAnimation on width {
                            from: 0
                            to: 100
                            duration: 2000
                            loops: Animation.Infinite
                        }
                    }
                }
            }
        }
    }
}
