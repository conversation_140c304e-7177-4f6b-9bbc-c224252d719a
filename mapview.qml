import QtQuick
import QtLocation
import QtPositioning

Rectangle {
    id: mapContainer
    width: 800
    height: 600

    property var measurePoints: []
    property bool measureMode: false

    // 信号定义
    signal moveToBeijing()
    signal startMeasure()

    Plugin {
        id: mapPlugin
        name: "osm" // 使用OpenStreetMap
        PluginParameter {
            name: "osm.mapping.providersrepository.disabled"
            value: "true"
        }
        PluginParameter {
            name: "osm.mapping.providersrepository.address"
            value: "http://maps-redirect.qt.io/osm/5.6/"
        }
    }

    Map {
        id: map
        anchors.fill: parent
        plugin: mapPlugin
        center: QtPositioning.coordinate(39.9042, 116.4074) // 北京坐标
        zoomLevel: 10

        // 设置缩放范围
        minimumZoomLevel: 1
        maximumZoomLevel: 20

        property geoCoordinate startCentroid

        // 禁用原生手势控制，我们使用自定义手势处理器
        // 注意：不再使用过时的gesture属性

        // 缩放手势处理（双指缩放）
        PinchHandler {
            id: pinch
            target: null
            enabled: !measureMode
            onActiveChanged: if (active) {
                map.startCentroid = map.toCoordinate(pinch.centroid.position, false)
            }
            onScaleChanged: (delta) => {
                map.zoomLevel += Math.log2(delta)
                map.alignCoordinateToPoint(map.startCentroid, pinch.centroid.position)
            }
            onRotationChanged: (delta) => {
                map.bearing -= delta
                map.alignCoordinateToPoint(map.startCentroid, pinch.centroid.position)
            }
            grabPermissions: PointerHandler.TakeOverForbidden
        }

        // 鼠标滚轮缩放处理
        WheelHandler {
            id: wheel
            enabled: !measureMode
            acceptedDevices: Qt.platform.pluginName === "cocoa" || Qt.platform.pluginName === "wayland"
                             ? PointerDevice.Mouse | PointerDevice.TouchPad
                             : PointerDevice.Mouse
            rotationScale: 1/120
            property: "zoomLevel"
        }

        // 拖动手势处理
        DragHandler {
            id: drag
            target: null
            enabled: !measureMode
            onTranslationChanged: (delta) => map.pan(-delta.x, -delta.y)
        }

        // 处理鼠标点击事件（仅在测距模式下）
        MouseArea {
            anchors.fill: parent
            enabled: measureMode

            onClicked: {
                if (measureMode) {
                    var coord = map.toCoordinate(Qt.point(mouse.x, mouse.y))
                    measurePoints.push(coord)

                    if (measurePoints.length === 1) {
                        // 添加第一个点标记
                        var marker1 = Qt.createQmlObject(`
                            import QtQuick
                            import QtLocation
                            MapQuickItem {
                                anchorPoint.x: sourceItem.width / 2
                                anchorPoint.y: sourceItem.height
                                sourceItem: Rectangle {
                                    width: 20; height: 20; radius: 10
                                    color: "red"; border.color: "darkred"; border.width: 2
                                    Text { anchors.centerIn: parent; text: "1"; color: "white"; font.bold: true }
                                }
                            }`, map, "marker1")
                        marker1.coordinate = coord
                        map.addMapItem(marker1)
                        statusText.text = "请点击第二个点"
                    } else if (measurePoints.length === 2) {
                        // 添加第二个点标记
                        var marker2 = Qt.createQmlObject(`
                            import QtQuick
                            import QtLocation
                            MapQuickItem {
                                anchorPoint.x: sourceItem.width / 2
                                anchorPoint.y: sourceItem.height
                                sourceItem: Rectangle {
                                    width: 20; height: 20; radius: 10
                                    color: "blue"; border.color: "darkblue"; border.width: 2
                                    Text { anchors.centerIn: parent; text: "2"; color: "white"; font.bold: true }
                                }
                            }`, map, "marker2")
                        marker2.coordinate = coord
                        map.addMapItem(marker2)

                        // 计算距离
                        var distance = measurePoints[0].distanceTo(measurePoints[1])
                        var distanceKm = (distance / 1000).toFixed(2)

                        // 添加连线
                        var polyline = Qt.createQmlObject(`
                            import QtQuick
                            import QtLocation
                            MapPolyline {
                                line.width: 3; line.color: "blue"; opacity: 0.8
                            }`, map, "polyline")
                        polyline.path = measurePoints
                        map.addMapItem(polyline)

                        statusText.text = "距离: " + distanceKm + " 公里"
                        measureMode = false
                        measurePoints = []
                    }
                }
            }
        }
    }

    // 状态文本
    Rectangle {
        id: statusBar
        anchors.bottom: parent.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        height: 60
        color: "lightgray"
        opacity: 0.8

        Column {
            anchors.centerIn: parent
            spacing: 2

            Text {
                id: statusText
                anchors.horizontalCenter: parent.horizontalCenter
                text: "地图已加载 - 可拖动和缩放"
                font.pixelSize: 14
            }

            Text {
                id: mapInfoText
                anchors.horizontalCenter: parent.horizontalCenter
                text: "缩放级别: " + map.zoomLevel.toFixed(1) + " | 中心: " +
                      map.center.latitude.toFixed(4) + ", " + map.center.longitude.toFixed(4)
                font.pixelSize: 10
                color: "darkgray"
            }
        }
    }

    // 清除按钮
    Rectangle {
        id: clearButton
        anchors.top: parent.top
        anchors.right: parent.right
        anchors.margins: 10
        width: 80
        height: 30
        color: "lightblue"
        border.color: "blue"
        radius: 5

        Text {
            anchors.centerIn: parent
            text: "清除"
            font.pixelSize: 12
        }

        MouseArea {
            anchors.fill: parent
            onClicked: {
                clearMap()
            }
        }
    }

    // 响应外部信号
    function moveToBeijingLocation() {
        map.center = QtPositioning.coordinate(39.9042, 116.4074)
        map.zoomLevel = 12
        statusText.text = "已移动到北京 - 可拖动和缩放"
    }

    function startMeasureMode() {
        measureMode = true
        measurePoints = []
        map.clearMapItems()
        statusText.text = "测距模式：请点击第一个点"
    }

    // 更新清除按钮的功能
    function clearMap() {
        map.clearMapItems()
        measurePoints = []
        measureMode = false
        statusText.text = "地图已清除 - 可拖动和缩放"
    }
}
