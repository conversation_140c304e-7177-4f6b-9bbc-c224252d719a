import QtQuick
import QtQuick.Controls
import QtLocation
import QtPositioning

ApplicationWindow {
    id: window
    width: 1000
    height: 700
    visible: true
    title: "TMS瓦片地图测试"

    Rectangle {
        anchors.fill: parent
        color: "lightgray"

        // 加载我们的地图组件
        Loader {
            id: mapLoader
            anchors.fill: parent
            source: "mapview.qml"
            
            onLoaded: {
                console.log("地图组件已加载")
            }
        }

        // 测试按钮
        Row {
            anchors.top: parent.top
            anchors.left: parent.left
            anchors.margins: 10
            spacing: 10

            Rectangle {
                width: 100
                height: 40
                color: "lightblue"
                border.color: "blue"
                radius: 5

                Text {
                    anchors.centerIn: parent
                    text: "移动到北京"
                    font.pixelSize: 12
                }

                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        if (mapLoader.item) {
                            mapLoader.item.moveToBeijingLocation()
                        }
                    }
                }
            }

            Rectangle {
                width: 80
                height: 40
                color: "lightgreen"
                border.color: "green"
                radius: 5

                Text {
                    anchors.centerIn: parent
                    text: "测距"
                    font.pixelSize: 12
                }

                Mouse<PERSON>rea {
                    anchors.fill: parent
                    onClicked: {
                        if (mapLoader.item) {
                            mapLoader.item.startMeasureMode()
                        }
                    }
                }
            }
        }

        // 信息显示
        Rectangle {
            anchors.top: parent.top
            anchors.right: parent.right
            anchors.margins: 10
            width: 300
            height: 100
            color: "white"
            border.color: "gray"
            radius: 5
            opacity: 0.9

            Column {
                anchors.fill: parent
                anchors.margins: 10
                spacing: 5

                Text {
                    text: "TMS瓦片地图测试"
                    font.bold: true
                    font.pixelSize: 14
                }

                Text {
                    text: "瓦片服务器: ReadyMap.org"
                    font.pixelSize: 12
                    color: "darkblue"
                }

                Text {
                    text: "URL: http://readymap.org/readymap/tiles/1.0.0/7/"
                    font.pixelSize: 10
                    color: "gray"
                    wrapMode: Text.WordWrap
                    width: parent.width
                }

                Text {
                    text: "支持拖动、缩放和测距功能"
                    font.pixelSize: 10
                    color: "darkgreen"
                }
            }
        }
    }
}
