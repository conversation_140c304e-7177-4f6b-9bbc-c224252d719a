//
// Created by cap_s on 2025/6/25.
//

// You may need to build the project (run Qt uic code generator) to get "ui_MainWindow.h" resolved

#include "mainwindow.h"
#include "ui_MainWindow.h"
#include <QQmlEngine>
#include <QQmlContext>
#include <QApplication>
#include <QDir>
#include <QFile>
#include <QUrl>
#include <QMenu>
#include <QQuickItem>

MainWindow::MainWindow(QWidget *parent) :
    QMainWindow(parent), ui(new Ui::MainWindow) {
    ui->setupUi(this);
    setupUI();
    setupMenus();
}

MainWindow::~MainWindow() {
    delete ui;
}

void MainWindow::setupUI() {
    // 设置窗口标题和大小
    setWindowTitle("QML地图应用");
    resize(1000, 700);

    // 创建QQuickWidget来显示QML地图
    mapWidget = new QQuickWidget(this);
    mapWidget->setResizeMode(QQuickWidget::SizeRootObjectToView);

    // 设置QML文件路径
    QString qmlPath = QApplication::applicationDirPath() + "/../mapview.qml";
    if (!QFile::exists(qmlPath)) {
        // 如果在应用程序目录找不到，尝试在源码目录
        qmlPath = QDir::currentPath() + "/../mapview.qml";
    }

    mapWidget->setSource(QUrl::fromLocalFile(qmlPath));

    // 将地图控件设置为中央控件
    setCentralWidget(mapWidget);
}

void MainWindow::setupMenus() {
    // 创建菜单栏
    QMenuBar *menuBar = this->menuBar();
    QMenu *mapMenu = menuBar->addMenu("地图操作");

    // 创建北京Action
    beijingAction = new QAction("移动到北京", this);
    beijingAction->setShortcut(QKeySequence("Ctrl+B"));
    beijingAction->setStatusTip("将地图中心移动到北京");
    connect(beijingAction, &QAction::triggered, this, &MainWindow::moveToBeijing);
    mapMenu->addAction(beijingAction);

    // 创建测距Action
    measureAction = new QAction("测距", this);
    measureAction->setShortcut(QKeySequence("Ctrl+M"));
    measureAction->setStatusTip("测量两点之间的距离");
    connect(measureAction, &QAction::triggered, this, &MainWindow::startMeasure);
    mapMenu->addAction(measureAction);

    // 添加分隔符
    mapMenu->addSeparator();

    // 创建地图切换子菜单
    QMenu *mapTypeMenu = mapMenu->addMenu("地图类型");

    // 创建OSM地图Action
    osmAction = new QAction("OpenStreetMap", this);
    osmAction->setShortcut(QKeySequence("Ctrl+1"));
    osmAction->setStatusTip("切换到OpenStreetMap地图");
    osmAction->setCheckable(true);
    osmAction->setChecked(true); // 默认选中OSM
    connect(osmAction, &QAction::triggered, this, &MainWindow::switchToOSM);
    mapTypeMenu->addAction(osmAction);

    // 创建电子海图Action
    nauticalAction = new QAction("电子海图", this);
    nauticalAction->setShortcut(QKeySequence("Ctrl+2"));
    nauticalAction->setStatusTip("切换到电子海图");
    nauticalAction->setCheckable(true);
    connect(nauticalAction, &QAction::triggered, this, &MainWindow::switchToNauticalChart);
    mapTypeMenu->addAction(nauticalAction);
}

void MainWindow::moveToBeijing() {
    // 调用QML中的函数
    QQuickItem *rootObject = mapWidget->rootObject();
    if (rootObject) {
        QMetaObject::invokeMethod(rootObject, "moveToBeijingLocation");
    }
}

void MainWindow::startMeasure() {
    // 调用QML中的函数
    QQuickItem *rootObject = mapWidget->rootObject();
    if (rootObject) {
        QMetaObject::invokeMethod(rootObject, "startMeasureMode");
    }
}

void MainWindow::switchToOSM() {
    // 设置互斥选择
    osmAction->setChecked(true);
    nauticalAction->setChecked(false);

    // 调用QML中的函数切换到OSM
    QQuickItem *rootObject = mapWidget->rootObject();
    if (rootObject) {
        QMetaObject::invokeMethod(rootObject, "switchToOSMMap");
    }
}

void MainWindow::switchToNauticalChart() {
    // 设置互斥选择
    osmAction->setChecked(false);
    nauticalAction->setChecked(true);

    // 调用QML中的函数切换到电子海图
    QQuickItem *rootObject = mapWidget->rootObject();
    if (rootObject) {
        QMetaObject::invokeMethod(rootObject, "switchToNauticalMap");
    }
}
