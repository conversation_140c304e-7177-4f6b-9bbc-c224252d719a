import QtQuick
import QtLocation

MapQuickItem {
    id: marker
    property string text: ""
    
    anchorPoint.x: sourceItem.width / 2
    anchorPoint.y: sourceItem.height
    
    sourceItem: Rectangle {
        width: 20
        height: 20
        radius: 10
        color: "red"
        border.color: "darkred"
        border.width: 2
        
        Text {
            anchors.centerIn: parent
            text: "●"
            color: "white"
            font.pixelSize: 8
            font.bold: true
        }
        
        Rectangle {
            anchors.top: parent.bottom
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.topMargin: 2
            width: Math.max(40, textLabel.width + 8)
            height: 20
            color: "white"
            border.color: "black"
            border.width: 1
            radius: 3
            
            Text {
                id: textLabel
                anchors.centerIn: parent
                text: marker.text
                font.pixelSize: 10
                color: "black"
            }
        }
    }
}
