//
// Created by cap_s on 2025/6/25.
//

#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QQuickWidget>
#include <QVBoxLayout>
#include <QAction>
#include <QMenuBar>


QT_BEGIN_NAMESPACE
namespace Ui { class MainWindow; }
QT_END_NAMESPACE

class MainWindow : public QMainWindow {
Q_OBJECT

public:
    explicit MainWindow(QWidget *parent = nullptr);
    ~MainWindow() override;

private slots:
    void moveToBeijing();
    void startMeasure();

private:
    Ui::MainWindow *ui;
    QQuickWidget *mapWidget{};
    QAction *beijingAction{};
    QAction *measureAction{};

    void setupUI();
    void setupMenus();
};


#endif //MAINWINDOW_H
